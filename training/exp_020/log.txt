Epoch	Train	Validation
epoch: 1, step: 74, time: 45.7, train: 23.565, valid: 17.468, train_acc: 0.072, valid_acc: 0.113
epoch: 2, step: 148, time: 36.1, train: 17.790, valid: 15.872, train_acc: 0.108, valid_acc: 0.133
epoch: 3, step: 221, time: 41.2, train: 16.398, valid: 14.980, train_acc: 0.128, valid_acc: 0.154
epoch: 4, step: 294, time: 39.3, train: 15.417, valid: 15.041, train_acc: 0.148, valid_acc: 0.153
epoch: 5, step: 369, time: 40.8, train: 14.736, valid: 13.124, train_acc: 0.167, valid_acc: 0.202
epoch: 6, step: 444, time: 39.7, train: 13.954, valid: 12.683, train_acc: 0.187, valid_acc: 0.209
epoch: 7, step: 515, time: 41.2, train: 13.665, valid: 12.079, train_acc: 0.193, valid_acc: 0.229
epoch: 8, step: 586, time: 39.6, train: 13.105, valid: 11.938, train_acc: 0.208, valid_acc: 0.237
epoch: 9, step: 656, time: 41.0, train: 12.714, valid: 11.232, train_acc: 0.219, valid_acc: 0.248
epoch: 10, step: 726, time: 37.7, train: 12.391, valid: 11.386, train_acc: 0.225, valid_acc: 0.249
epoch: 11, step: 796, time: 39.2, train: 12.098, valid: 10.990, train_acc: 0.231, valid_acc: 0.261
epoch: 12, step: 866, time: 37.0, train: 11.845, valid: 10.554, train_acc: 0.238, valid_acc: 0.267
epoch: 13, step: 940, time: 42.0, train: 11.742, valid: 10.673, train_acc: 0.240, valid_acc: 0.270
epoch: 14, step: 1014, time: 38.7, train: 11.503, valid: 10.455, train_acc: 0.245, valid_acc: 0.271
epoch: 15, step: 1089, time: 42.3, train: 11.284, valid: 10.303, train_acc: 0.251, valid_acc: 0.278
epoch: 16, step: 1164, time: 40.1, train: 11.335, valid: 9.982, train_acc: 0.249, valid_acc: 0.285
epoch: 17, step: 1239, time: 43.7, train: 10.959, valid: 9.796, train_acc: 0.260, valid_acc: 0.292
epoch: 18, step: 1314, time: 36.3, train: 10.726, valid: 9.472, train_acc: 0.265, valid_acc: 0.301
epoch: 19, step: 1383, time: 44.5, train: 10.730, valid: 9.604, train_acc: 0.267, valid_acc: 0.295
epoch: 20, step: 1452, time: 34.0, train: 10.583, valid: 9.378, train_acc: 0.270, valid_acc: 0.305
epoch: 21, step: 1522, time: 41.2, train: 10.396, valid: 9.458, train_acc: 0.275, valid_acc: 0.304
epoch: 22, step: 1592, time: 37.0, train: 10.324, valid: 9.444, train_acc: 0.277, valid_acc: 0.301
epoch: 23, step: 1662, time: 39.9, train: 10.250, valid: 9.518, train_acc: 0.278, valid_acc: 0.302
epoch: 24, step: 1732, time: 38.0, train: 10.092, valid: 9.048, train_acc: 0.284, valid_acc: 0.315
epoch: 25, step: 1808, time: 46.0, train: 10.221, valid: 8.969, train_acc: 0.279, valid_acc: 0.322
epoch: 26, step: 1884, time: 36.7, train: 10.010, valid: 8.876, train_acc: 0.285, valid_acc: 0.320
epoch: 27, step: 1959, time: 43.3, train: 10.013, valid: 8.652, train_acc: 0.286, valid_acc: 0.328
epoch: 28, step: 2034, time: 40.4, train: 9.694, valid: 8.779, train_acc: 0.296, valid_acc: 0.324

{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "collapsed_sections": [], "authorship_tag": "ABX9TyNOXLieRc+7DSEUOVtmG9NS", "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/dauparas/ProteinMPNN/blob/main/training/colab_training_example.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Boc3uL_qWUKy", "outputId": "f337b98c-48b5-4e3f-d1da-b38b08aca48f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Cloning into 'ProteinMPNN'...\n", "remote: Enumerating objects: 542, done.\u001b[K\n", "remote: Counting objects: 100% (542/542), done.\u001b[K\n", "remote: Compressing objects: 100% (297/297), done.\u001b[K\n", "remote: Total 542 (delta 236), reused 508 (delta 226), pack-reused 0\u001b[K\n", "Receiving objects: 100% (542/542), 66.58 MiB | 13.73 MiB/s, done.\n", "Resolving deltas: 100% (236/236), done.\n"]}], "source": ["!git clone https://github.com/dauparas/ProteinMPNN.git"]}, {"cell_type": "code", "source": ["!wget https://files.ipd.uw.edu/pub/training_sets/pdb_2021aug02_sample.tar.gz"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "lPQopPm1WWrG", "outputId": "9546d08c-9248-445e-81b0-b3ba82f4a801"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["--2022-10-26 03:43:33--  https://files.ipd.uw.edu/pub/training_sets/pdb_2021aug02_sample.tar.gz\n", "Resolving files.ipd.uw.edu (files.ipd.uw.edu)... ************, **************, **************, ...\n", "Connecting to files.ipd.uw.edu (files.ipd.uw.edu)|************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 49690915 (47M) [application/octet-stream]\n", "Saving to: ‘pdb_2021aug02_sample.tar.gz’\n", "\n", "pdb_2021aug02_sampl 100%[===================>]  47.39M  13.5MB/s    in 3.7s    \n", "\n", "2022-10-26 03:43:38 (12.9 MB/s) - ‘pdb_2021aug02_sample.tar.gz’ saved [49690915/49690915]\n", "\n"]}]}, {"cell_type": "code", "source": ["!tar xvf \"pdb_2021aug02_sample.tar.gz\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "21to7lSNWemb", "outputId": "7a51e458-a4e4-4b79-d26b-81570e855d62"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["./pdb_2021aug02_sample/\n", "./pdb_2021aug02_sample/README\n", "./pdb_2021aug02_sample/list.csv\n", "./pdb_2021aug02_sample/pdb/\n", "./pdb_2021aug02_sample/pdb/l3/\n", "./pdb_2021aug02_sample/pdb/l3/5l3p.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3g_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3f.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3r_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3y_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_DB.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l36_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3y_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l36.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3n.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3x.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3r_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3g_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3e.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l39_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l31_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l35.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l37_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_CB.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3x_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3u_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_S.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3m.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l38.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3v.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_L.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3v_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_I.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3n_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3e_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l32_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3t_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_I.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3d_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3u.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3k.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l33_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3g_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3i_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3q_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l33.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3o_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3z_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3t.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l38_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3b_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l32.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3i_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3g_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l33_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3j.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3v_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3c_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3a.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l32_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l39.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3h_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l32_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3c_J.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3c_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l39_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l31_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3r.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l39_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3l.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_R.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3u_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3z.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3a_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3g.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3r_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3q.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l30_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3y.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3o.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l37.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3a_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l36.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_CA.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l37_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l34_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3x.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3m_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3n.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3x_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_P.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l39_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l37_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3p.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3m_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l35.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3l_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l36_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3m.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3r_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3g_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3r_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3e.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_DA.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3y_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l36_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3h.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3q_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3l_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3o_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l30.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3t_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3o_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3b_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l35_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l38.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_J.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_JA.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3q_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3v.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l34_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_J.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3c_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3m_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3n_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3p_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_MA.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3p_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3u.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3c.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l34_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l31_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l32_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3h_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3c_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_K.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l39_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3v_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_FA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3c_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3b.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_VA.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l32_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3h_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_AA.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l31.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_QA.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3g_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3i_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3i.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l33_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3i_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3w.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3a.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l33_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_K.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l39.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3b_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3w_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3t_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3j_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_OA.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3z.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3l.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l30_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l34.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3d.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3g_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3r.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l30_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_Q.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3u_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l37.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_HA.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l31_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3k_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_XA.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3u_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3q.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3g.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3l_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l36_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_O.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3s.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3e.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3m.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3r_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3g_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3l_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l35.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l36_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l37_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3f.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3p.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_J.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3m_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l37_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_Y.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3m_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_O.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3f_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3u.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l39_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3n_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3p_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3p_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l34_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3k.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_U.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3u_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3n_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3r_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3q_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3v.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3z_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l35_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l30.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3h.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3d_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3q_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l39.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l38_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3w_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3w.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3i_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3a.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l33_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3d_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3i.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3w_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3b_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l31.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l32_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l31_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3h_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3b.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l39_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3t.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3u_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3v_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_T.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3j.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l32.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l32_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3h_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_K.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3u_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3q.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3g.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l31_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3k_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l31_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3f_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_N.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3u_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_BB.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_X.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3j_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3r.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_N.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_EB.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3t_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3a_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l34.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3a_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3t_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3g_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3j_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3z.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3l.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l33_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l37_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_Z.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3m_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_L.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3f_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l31_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_IA.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3f_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3e.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3x_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_I.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3n.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_NA.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3x.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3r_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3g_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3y_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l36.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3l_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3t_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l36_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_L.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3p.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3f.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l33.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_PA.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3d_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3q_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l33_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3c.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3q_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3d_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3u.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3z_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l30.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l34_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_V.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3u_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3v_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3h.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3n_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3v.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_GA.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l38.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_WA.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3e_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3h_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3p_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3i.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3v_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_W.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l34_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l39_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l31.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_LA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3h_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l39.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l39_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3a.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3w.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3q_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3j.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l32.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3b_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l38_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3t.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_KA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3b.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3q_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l33_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3a_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3l_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l37.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3y.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3o.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3g.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3q.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3l_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_M.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_EA.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3t_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3a_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l31_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3u_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_M.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_BA.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3c_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3m_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3r.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3d.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3k_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3s_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_MA.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3n_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l34_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l31_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3h.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3p_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3e_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3v.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3n_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l38.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l34_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3d_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3q_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l33.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l30_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l35_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3o_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l35_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3c.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_JA.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3u.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3t_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3o_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3q_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l36_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3n.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3r_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l36.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3i_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3g_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_DA.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3r_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3p.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_TA.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3f.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3o_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3l_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_CA.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3m.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3h_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l35.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_SA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l37_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3x_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3n_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l37_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_I.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_R.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3u_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l34.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3h_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l31_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3l.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_S.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l32_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l31_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_HA.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3c_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l37.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l30_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3j_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3a_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_OA.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3y.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3t_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3o.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3g.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3q.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_L.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l33_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l30_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3g_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3j_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3i_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3j.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3b_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l38_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_AA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l33_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l30_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l32.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l33_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3i_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3t_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3t.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3b.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3e_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3h_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3i.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l32_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l31_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l31.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l39.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3v_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3a.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3h_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_I.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l33_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3z_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3t_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l38.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3d_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3q_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l30.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l30_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l35_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3o_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3h.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3z_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_K.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3u.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3c.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3v_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l33.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3n_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l34_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3k.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l32_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l31_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3e_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3u_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3x_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l31_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_J.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3f_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_Q.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3n.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3h_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3f_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l36.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l37_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3j_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3i_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3r_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3s.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3e.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3y_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l36_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_Q.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3y_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3m.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3r_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3g_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3d.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3r.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3j_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l34.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3z.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3a_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3t_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_P.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_P.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_K.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l31_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3c_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3s_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l37_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l37.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3o.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l31_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3y.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l39_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3v_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3b.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_J.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l32_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3j.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l39_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3c_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3n_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l32.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3i_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l39.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3b_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3w.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3a.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l38_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3i.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3a_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3b_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l38_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l35_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_AB.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l31.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3q_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3k.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3h_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3e_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l33.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3n_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_GA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l32_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_T.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3e_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_PA.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3l_G.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3z_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3h.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l30.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l38.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3i_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_K.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l33_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l35_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3v.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3o_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3t_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3g_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l30_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l35.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_NA.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3t_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3m.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3y_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3s.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l36_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3e.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3t_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3g_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l36.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3x_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3x.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3n.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l31_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3f_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3e_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3k_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3s_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_X.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3f.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_N.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3f_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3p.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3x_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3v_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_IA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3k_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l37_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_BA.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3k_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3e_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l31_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3o.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3y.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_RA.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l37.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3q.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_O.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3g.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_Y.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3z.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3a_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3l.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l35_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l34.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l30_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_EA.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3t_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3a_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_UA.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3d.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3r.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l31.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3l_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l38_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_KA.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3b_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3o_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3w.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l38_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3a.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l33_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_J.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3i_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l39.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_LA.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l39_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l32.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3c_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3h_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_F.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3j.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_N.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_U.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l32_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l39_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3v_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3c_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3n_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3q_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l35_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3c.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3u.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3o_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l35_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3o_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3z_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l33.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3n_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l34_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l31_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_W.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3e_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_L.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3h.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3p_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3e_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3n_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l30.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l37_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l34_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3s_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_M.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3v_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l37_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3m_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3n_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l35.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3x_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3s_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3p.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3f.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l36_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3g_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3r_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3q_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l36.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l35_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l36_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3y_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3n.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3a_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3l_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3x.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l30_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3g_C.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3t_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3g.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3q.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3y.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3a_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3o.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l35_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l37.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l30_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3j_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3v_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3u_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3k_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_L.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3r.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3c_Z.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3d.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l31_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3k_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3l.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l31_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l32_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3z.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l34.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3n_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3f_H.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3x_M.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3p_V.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l31_D.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l32_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l39_B.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3v_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l37_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l31.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3v_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l3h_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/3l3s_E.pt\n", "./pdb_2021aug02_sample/pdb/l3/7l3i.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l32_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/2l3w_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3t.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3b.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l38_I.pt\n", "./pdb_2021aug02_sample/pdb/l3/4l33_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3i_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l32.pt\n", "./pdb_2021aug02_sample/pdb/l3/5l3i_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/6l3w_A.pt\n", "./pdb_2021aug02_sample/pdb/l3/1l3l_E.pt\n", "./pdb_2021aug02_sample/test_clusters.txt\n", "./pdb_2021aug02_sample/valid_clusters.txt\n"]}]}, {"cell_type": "code", "source": ["import sys"], "metadata": {"id": "Ul9pmaQzXAg3"}, "execution_count": 4, "outputs": []}, {"cell_type": "code", "source": ["sys.path.append(\"/content/ProteinMPNN/training\")"], "metadata": {"id": "o-03W_vgWguT"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["from training import main as run_training"], "metadata": {"id": "6-xXsyM-W_7k"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["# argparser = argparse.ArgumentParser(formatter_class=argparse.ArgumentDefaultsHelpFormatter)\n", "\n", "# argparser.add_argument(\"--path_for_training_data\", type=str, default=\"my_path/pdb_2021aug02\", help=\"path for loading training data\") \n", "# argparser.add_argument(\"--path_for_outputs\", type=str, default=\"./test\", help=\"path for logs and model weights\")\n", "# argparser.add_argument(\"--previous_checkpoint\", type=str, default=\"\", help=\"path for previous model weights, e.g. file.pt\")\n", "# argparser.add_argument(\"--num_epochs\", type=int, default=200, help=\"number of epochs to train for\")\n", "# argparser.add_argument(\"--save_model_every_n_epochs\", type=int, default=10, help=\"save model weights every n epochs\")\n", "# argparser.add_argument(\"--reload_data_every_n_epochs\", type=int, default=2, help=\"reload training data every n epochs\")\n", "# argparser.add_argument(\"--num_examples_per_epoch\", type=int, default=1000000, help=\"number of training example to load for one epoch\")\n", "# argparser.add_argument(\"--batch_size\", type=int, default=10000, help=\"number of tokens for one batch\")\n", "# argparser.add_argument(\"--max_protein_length\", type=int, default=10000, help=\"maximum length of the protein complext\")\n", "# argparser.add_argument(\"--hidden_dim\", type=int, default=128, help=\"hidden model dimension\")\n", "# argparser.add_argument(\"--num_encoder_layers\", type=int, default=3, help=\"number of encoder layers\") \n", "# argparser.add_argument(\"--num_decoder_layers\", type=int, default=3, help=\"number of decoder layers\")\n", "# argparser.add_argument(\"--num_neighbors\", type=int, default=48, help=\"number of neighbors for the sparse graph\")   \n", "# argparser.add_argument(\"--dropout\", type=float, default=0.1, help=\"dropout level; 0.0 means no dropout\")\n", "# argparser.add_argument(\"--backbone_noise\", type=float, default=0.2, help=\"amount of noise added to backbone during training\")   \n", "# argparser.add_argument(\"--rescut\", type=float, default=3.5, help=\"PDB resolution cutoff\")\n", "# argparser.add_argument(\"--debug\", type=bool, default=False, help=\"minimal data loading for debugging\")\n", "# argparser.add_argument(\"--gradient_norm\", type=float, default=-1.0, help=\"clip gradient norm, set to negative to omit clipping\")\n", "# argparser.add_argument(\"--mixed_precision\", type=bool, default=True, help=\"train with mixed precision\")\n", "\n", "# args = argparser.parse_args()    \n", "# main(args)\n", "\n", "class MyArgs(object):\n", "  def __init__(self):\n", "    self.path_for_training_data = \"/content/pdb_2021aug02_sample\"\n", "    self.path_for_outputs = \"/content/test\"\n", "    self.previous_checkpoint = \"\"\n", "    self.num_epochs = 50\n", "    self.save_model_every_n_epochs = 5\n", "    self.reload_data_every_n_epochs = 4\n", "    self.num_examples_per_epoch = 200\n", "    self.batch_size = 2000\n", "    self.max_protein_length = 2000\n", "    self.hidden_dim = 128\n", "    self.num_encoder_layers = 3\n", "    self.num_decoder_layers = 3\n", "    self.num_neighbors = 32\n", "    self.dropout = 0.1\n", "    self.backbone_noise = 0.1\n", "    self.rescut = 3.5\n", "    self.debug = False\n", "    self.gradient_norm = -1.0 #no norm\n", "    self.mixed_precision= True \n", "\n", "args = MyArgs()"], "metadata": {"id": "mks1E1FTXGXL"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["#epoch - number of times data is trained on\n", "#step - number of optimizer steps\n", "#time - time in seconds for one epoch to finish\n", "#train - training perplexity = exp(average categorical cross entropy)\n", "#valid - validation perplexity\n", "#train_acc - training accuracy\n", "#valid_acc - validation accuracy"], "metadata": {"id": "6csB6zUrbcAH"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["run_training(args)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "68wFaZ0IXHzj", "outputId": "52c84f9e-599b-4ca4-b46f-25170828a310"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.7/dist-packages/torch/utils/data/dataloader.py:566: UserWarning: This DataLoader will create 4 worker processes in total. Our suggested max number of worker in current system is 2, which is smaller than what this DataLoader is going to create. Please be aware that excessive worker creation might get DataLoader running slow or even freeze, lower the worker number to avoid potential slowness/freeze if necessary.\n", "  cpuset_checked))\n", "/usr/local/lib/python3.7/dist-packages/torch/utils/checkpoint.py:25: UserWarning: None of the inputs have requires_grad=True. Gradients will be None\n", "  warnings.warn(\"None of the inputs have requires_grad=True. Gradients will be None\")\n"]}, {"output_type": "stream", "name": "stdout", "text": ["epoch: 1, step: 4, time: 1.3, train: 32.478, valid: 26.652, train_acc: 0.057, valid_acc: 0.060\n", "epoch: 2, step: 8, time: 1.4, train: 31.076, valid: 24.790, train_acc: 0.056, valid_acc: 0.067\n", "epoch: 3, step: 12, time: 0.9, train: 28.561, valid: 22.354, train_acc: 0.058, valid_acc: 0.075\n", "epoch: 4, step: 16, time: 0.9, train: 25.958, valid: 20.493, train_acc: 0.063, valid_acc: 0.090\n", "epoch: 5, step: 22, time: 1.6, train: 23.583, valid: 19.557, train_acc: 0.065, valid_acc: 0.114\n", "epoch: 6, step: 28, time: 1.5, train: 21.809, valid: 19.653, train_acc: 0.069, valid_acc: 0.104\n", "epoch: 7, step: 34, time: 1.5, train: 20.483, valid: 19.440, train_acc: 0.074, valid_acc: 0.105\n", "epoch: 8, step: 40, time: 1.2, train: 19.474, valid: 19.000, train_acc: 0.097, valid_acc: 0.122\n", "epoch: 9, step: 46, time: 2.0, train: 19.268, valid: 17.082, train_acc: 0.093, valid_acc: 0.116\n", "epoch: 10, step: 52, time: 2.3, train: 18.658, valid: 16.812, train_acc: 0.097, valid_acc: 0.109\n", "epoch: 11, step: 58, time: 2.3, train: 18.158, valid: 16.962, train_acc: 0.105, valid_acc: 0.120\n", "epoch: 12, step: 64, time: 1.7, train: 17.705, valid: 16.780, train_acc: 0.109, valid_acc: 0.120\n", "epoch: 13, step: 69, time: 32.2, train: 18.135, valid: 16.984, train_acc: 0.105, valid_acc: 0.116\n", "epoch: 14, step: 74, time: 1.9, train: 18.008, valid: 17.287, train_acc: 0.106, valid_acc: 0.120\n", "epoch: 15, step: 79, time: 2.0, train: 17.833, valid: 17.172, train_acc: 0.118, valid_acc: 0.111\n", "epoch: 16, step: 84, time: 1.7, train: 17.451, valid: 16.884, train_acc: 0.119, valid_acc: 0.115\n", "epoch: 17, step: 90, time: 3.0, train: 18.145, valid: 17.919, train_acc: 0.111, valid_acc: 0.129\n", "epoch: 18, step: 96, time: 2.8, train: 17.651, valid: 17.883, train_acc: 0.103, valid_acc: 0.103\n", "epoch: 19, step: 102, time: 2.0, train: 17.608, valid: 17.774, train_acc: 0.104, valid_acc: 0.126\n", "epoch: 20, step: 108, time: 2.3, train: 17.374, valid: 18.082, train_acc: 0.114, valid_acc: 0.132\n", "epoch: 21, step: 114, time: 31.8, train: 17.079, valid: 18.081, train_acc: 0.123, valid_acc: 0.117\n", "epoch: 22, step: 120, time: 2.5, train: 16.938, valid: 18.005, train_acc: 0.130, valid_acc: 0.133\n", "epoch: 23, step: 126, time: 1.6, train: 16.719, valid: 18.174, train_acc: 0.123, valid_acc: 0.116\n", "epoch: 24, step: 132, time: 1.2, train: 16.580, valid: 17.844, train_acc: 0.129, valid_acc: 0.136\n", "epoch: 25, step: 139, time: 2.6, train: 16.797, valid: 15.777, train_acc: 0.118, valid_acc: 0.125\n", "epoch: 26, step: 146, time: 2.7, train: 16.433, valid: 15.730, train_acc: 0.125, valid_acc: 0.134\n", "epoch: 27, step: 153, time: 1.8, train: 16.127, valid: 15.673, train_acc: 0.127, valid_acc: 0.135\n", "epoch: 28, step: 160, time: 1.8, train: 15.798, valid: 15.235, train_acc: 0.124, valid_acc: 0.148\n", "epoch: 29, step: 166, time: 31.4, train: 16.003, valid: 15.344, train_acc: 0.139, valid_acc: 0.146\n", "epoch: 30, step: 172, time: 2.7, train: 15.639, valid: 15.524, train_acc: 0.146, valid_acc: 0.133\n", "epoch: 31, step: 178, time: 1.9, train: 15.546, valid: 14.927, train_acc: 0.142, valid_acc: 0.153\n", "epoch: 32, step: 184, time: 1.7, train: 15.215, valid: 15.001, train_acc: 0.155, valid_acc: 0.155\n", "epoch: 33, step: 190, time: 4.9, train: 15.418, valid: 15.358, train_acc: 0.144, valid_acc: 0.137\n", "epoch: 34, step: 196, time: 3.2, train: 15.255, valid: 14.912, train_acc: 0.144, valid_acc: 0.152\n", "epoch: 35, step: 202, time: 1.9, train: 14.925, valid: 14.894, train_acc: 0.153, valid_acc: 0.156\n", "epoch: 36, step: 208, time: 2.0, train: 14.736, valid: 14.729, train_acc: 0.154, valid_acc: 0.168\n", "epoch: 37, step: 214, time: 32.7, train: 14.467, valid: 17.068, train_acc: 0.152, valid_acc: 0.140\n", "epoch: 38, step: 220, time: 2.8, train: 14.410, valid: 16.987, train_acc: 0.158, valid_acc: 0.122\n", "epoch: 39, step: 226, time: 2.0, train: 14.101, valid: 16.935, train_acc: 0.162, valid_acc: 0.123\n", "epoch: 40, step: 232, time: 2.4, train: 14.034, valid: 17.452, train_acc: 0.168, valid_acc: 0.131\n", "epoch: 41, step: 237, time: 4.0, train: 15.268, valid: 16.151, train_acc: 0.158, valid_acc: 0.147\n", "epoch: 42, step: 242, time: 2.2, train: 15.403, valid: 16.473, train_acc: 0.147, valid_acc: 0.148\n", "epoch: 43, step: 247, time: 1.6, train: 15.008, valid: 17.016, train_acc: 0.161, valid_acc: 0.129\n", "epoch: 44, step: 252, time: 2.0, train: 14.492, valid: 16.040, train_acc: 0.168, valid_acc: 0.152\n", "epoch: 45, step: 258, time: 32.4, train: 14.046, valid: 14.339, train_acc: 0.172, valid_acc: 0.161\n", "epoch: 46, step: 264, time: 2.4, train: 13.564, valid: 13.959, train_acc: 0.179, valid_acc: 0.156\n", "epoch: 47, step: 270, time: 1.8, train: 13.514, valid: 14.189, train_acc: 0.181, valid_acc: 0.151\n", "epoch: 48, step: 276, time: 1.5, train: 13.257, valid: 13.961, train_acc: 0.180, valid_acc: 0.160\n", "epoch: 49, step: 281, time: 4.1, train: 13.741, valid: 16.552, train_acc: 0.176, valid_acc: 0.154\n", "epoch: 50, step: 286, time: 2.2, train: 13.157, valid: 17.389, train_acc: 0.180, valid_acc: 0.130\n"]}]}]}
import os
import subprocess
import sys
import csv


def main():
    # 1) Define the three protease super‐folders (absolute or relative to interface.py)
    protease_folders = [
        "../inputs/dataset/Cysteine_Proteases/",
        "../inputs/dataset/Aspartic_Proteases/",
        "../inputs/dataset/Serine_Proteases/",
    ]

    # 2) Define the pH subfolders inside each protease super‐folder
    pdb_folders = [
        "ph_8_12",
        "ph_6_8",
        "ph_1_6",
    ]

    # 3) Build a dict mapping Uniprot ID → fixed_positions (1‐based),
    #    properly stripping off the brackets “[]” from the CSV.
    fixed_positions_dict = {}
    with open(
        "../fixed_positions_from_pdb.csv", newline="", encoding="utf-8"
    ) as csvfile:
        reader = csv.reader(csvfile)
        header = next(reader, None)  # skip header
        for row in reader:
            # row[2] = uniprot_id, row[4] = fixed_positions_1based (e.g. "[100, 281]")
            if len(row) < 5:
                continue
            uniport_id = row[2].strip()
            raw_list = row[4].strip()
            # strip leading/trailing brackets so we feed “100, 281” (no "[" or "]")
            fixed_list = raw_list.strip("[]")
            fixed_positions_dict[uniport_id] = fixed_list

    # 4) For each protease_folder, each pdb_folder, each model_weight:
    #    • Skip the (pH folder, bin) combinations that shouldn’t run together.
    #    • Inside that subfolder, run run_script.py on each .pdb, passing --fixed_positions as a bare “100,281,…”.
    for protease_folder in protease_folders:
        for pdb_folder in pdb_folders:
            for model_weight in ["p_48_bin1", "p_48_bin2", "p_48_bin3"]:
                # Skip invalid combinations:
                #   ph_1_6 → skip bin1
                #   ph_6_8 → skip bin2
                #   ph_8_12 → skip bin3
                if (
                    (pdb_folder == "ph_1_6" and model_weight == "p_48_bin1")
                    or (pdb_folder == "ph_6_8" and model_weight == "p_48_bin2")
                    or (pdb_folder == "ph_8_12" and model_weight == "p_48_bin3")
                ):
                    continue

                folder_path = os.path.join(protease_folder, pdb_folder)
                if not os.path.isdir(folder_path):
                    print(f"Warning: {folder_path} does not exist. Skipping.")
                    continue

                # Loop over every .pdb in that subfolder
                for pdb_file in os.listdir(folder_path):
                    if not pdb_file.endswith(".pdb"):
                        continue

                    uniport_id = pdb_file.split(".")[0]
                    fixed_list = fixed_positions_dict.get(uniport_id)
                    if fixed_list is None:
                        print(
                            f"Warning: No fixed positions found for UniProt={uniport_id}. Skipping {pdb_file}."
                        )
                        continue

                    cmd = [
                        sys.executable,
                        "run_script.py",
                        "--protease_folder",
                        protease_folder,
                        "--pdb_folder",
                        pdb_folder,
                        "--model_weight",
                        model_weight,
                        "--fixed_positions",
                        fixed_list,
                        "--pdb_file",
                        pdb_file,
                    ]
                    print(f"Running: {' '.join(cmd)}")
                    subprocess.run(cmd, check=True)


if __name__ == "__main__":
    main()

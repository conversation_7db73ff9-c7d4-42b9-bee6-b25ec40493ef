#!/usr/bin/env python3

import sys
import csv
import subprocess
import logging
from logging.handlers import Rota<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pathlib import Path
from concurrent.futures import ProcessPoolExecutor, as_completed

# ──────────────────────────────────────────────────────────────────────────────
# Configuration
# ──────────────────────────────────────────────────────────────────────────────

# List of protease super‐folders (relative to this script)
PROTEASE_FOLDERS = [
    Path("../inputs/dataset/Cysteine_Proteases"),
    Path("../inputs/dataset/Aspartic_Proteases"),
    Path("../inputs/dataset/Serine_Proteases"),
]

# pH subfolders inside each protease super‐folder
PDB_FOLDERS = ["ph_8_12", "ph_6_8", "ph_1_6"]

# Model weights to test
MODEL_WEIGHTS = ["p_48_bin1", "p_48_bin2", "p_48_bin3"]

# Skip rules: if PDB folder is key, any model_weight listed in its value is invalid
SKIP_COMBINATIONS = {
    "ph_1_6": ["p_48_bin1"],
    "ph_6_8": ["p_48_bin2"],
    "ph_8_12": ["p_48_bin3"],
}

# For Aspartic_Proteases, skip the "ph_8_12" folder entirely
SPECIAL_SKIP = {
    "Aspartic_Proteases": ["ph_8_12"]
}

# Location of fixed‐positions CSV (relative to script)
FIXED_POSITIONS_CSV = Path("../fixed_positions_from_pdb.csv")

# Helper scripts (relative to script)
HELPER_SCRIPTS_DIR = Path(__file__).parent / ".." / "helper_scripts"
PARSE_SCRIPT       = HELPER_SCRIPTS_DIR / "parse_multiple_chains.py"
ASSIGN_SCRIPT      = HELPER_SCRIPTS_DIR / "assign_fixed_chains.py"
MAKE_FIXED_SCRIPT  = HELPER_SCRIPTS_DIR / "make_fixed_positions_dict.py"
DESIGN_SCRIPT      = Path(__file__).parent / ".." / "protein_mpnn_run.py"

# Directory for outputs (relative to script)
OUTPUT_BASE_DIR = Path("..") / "outputs"

# Log file name
LOG_FILENAME = "pipeline.log"

# Maximum number of parallel workers for design jobs
MAX_WORKERS = min(4, (sys.platform == "win32") and 1 or None)  # if Windows, default to 1; otherwise use os.cpu_count()


# ──────────────────────────────────────────────────────────────────────────────
# Logger Setup
# ──────────────────────────────────────────────────────────────────────────────

def setup_logger(log_filename: str = LOG_FILENAME) -> logging.Logger:
    """
    Configure a rotating file logger that also prints to console, with timestamps.
    """
    logger = logging.getLogger("ProteasePipeline")
    logger.setLevel(logging.INFO)

    # Formatter with timestamp
    formatter = logging.Formatter(
        fmt="%(asctime)s [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Rotating file handler: 5 MB per file, keep up to 3 backups
    file_handler = RotatingFileHandler(log_filename, maxBytes=5_000_000, backupCount=3)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


logger = setup_logger()


# ──────────────────────────────────────────────────────────────────────────────
# Utility Functions
# ──────────────────────────────────────────────────────────────────────────────

def run_command(cmd: list, cwd: Path = None) -> None:
    """
    Run a subprocess command. Raises CalledProcessError on failure.
    """
    logger.info(f"Running command: {' '.join(str(x) for x in cmd)}")
    subprocess.run(cmd, check=True, cwd=(cwd or Path.cwd()))


def load_fixed_positions(csv_path: Path) -> dict:
    """
    Parse the CSV mapping UniProt ID → fixed_positions (comma-separated ints, no brackets).
    Returns a dict { uniprot_id: "int1, int2, …" }.
    """
    fixed_dict = {}
    if not csv_path.exists():
        logger.error(f"Fixed‐positions CSV not found: {csv_path}")
        sys.exit(1)

    try:
        with csv_path.open(newline="", encoding="utf-8") as f:
            reader = csv.reader(f)
            header = next(reader, None)  # skip header row if present
            for row in reader:
                if len(row) < 5:
                    continue
                uniprot_id = row[2].strip()
                raw_list = row[4].strip()          # e.g. "[100, 281]"
                fixed_list = raw_list.strip("[]")  # e.g. "100, 281"
                fixed_dict[uniprot_id] = fixed_list
    except Exception:
        logger.exception("Error while reading fixed‐positions CSV.")
        sys.exit(1)

    return fixed_dict


def needs_reparse(parsed_json: Path, pdb_folder: Path) -> bool:
    """
    Return True if parsed_json doesn't exist OR any .pdb in pdb_folder
    has a newer modification time than parsed_json.
    """
    if not parsed_json.exists():
        return True

    parsed_mtime = parsed_json.stat().st_mtime
    for pdb_file in pdb_folder.glob("*.pdb"):
        if pdb_file.stat().st_mtime > parsed_mtime:
            return True
    return False


def need_reassign(assigned_json: Path, parsed_json: Path) -> bool:
    """
    Return True if assigned_json doesn't exist OR parsed_json is newer.
    """
    if not assigned_json.exists():
        return True
    return parsed_json.stat().st_mtime > assigned_json.stat().st_mtime


# ──────────────────────────────────────────────────────────────────────────────
# Worker Function (for parallel design per‐PDB)
# ──────────────────────────────────────────────────────────────────────────────

def process_single_pdb(job: dict) -> tuple:
    """
    Worker for a single PDB “make_fixed_positions_dict” + “protein_mpnn_run” step.

    Expects job dict with keys:
      - protease_name       (str)
      - pdb_folder          (str)
      - model_weight        (str)
      - uniprot_id          (str)
      - fixed_list          (str, e.g. "100, 281")
      - folder_with_pdbs    (Path)
      - parsed_json         (Path)
      - assigned_json       (Path)

    Returns: (uniprot_id, model_weight, success: bool, message: str)
    """
    protease_name    = job["protease_name"]
    pdb_folder       = job["pdb_folder"]
    model_weight     = job["model_weight"]
    uniprot_id       = job["uniprot_id"]
    fixed_list       = job["fixed_list"]
    folder_with_pdbs = job["folder_with_pdbs"]
    parsed_json      = job["parsed_json"]
    assigned_json    = job["assigned_json"]

    try:
        # 1) Build output directory: outputs/<protease_name>/<pdb_folder>/<binX>/<uniprot_id>/
        bin_folder = model_weight.split("_")[-1]  # e.g. "bin1"
        output_dir = (
            OUTPUT_BASE_DIR
            / protease_name
            / pdb_folder
            / bin_folder
            / uniprot_id
        )
        output_dir.mkdir(parents=True, exist_ok=True)

        # 2) Path to the target PDB
        pdb_path = folder_with_pdbs / f"{uniprot_id}.pdb"
        if not pdb_path.exists():
            return (uniprot_id, model_weight, False, f"PDB not found: {pdb_path}")

        # 3) Paths for JSONL files
        path_fixed = output_dir / "fixed_pdbs.jsonl"

        # --- Step A: make_fixed_positions_dict.py ---
        cmd_fixed = [
            sys.executable,
            str(MAKE_FIXED_SCRIPT),
            "--input_path",    str(parsed_json),
            "--output_path",   str(path_fixed),
            "--chain_list",    "A",
            "--position_list", fixed_list,
        ]
        run_command(cmd_fixed)

        # --- Step B: protein_mpnn_run.py ---
        cmd_design = [
            sys.executable,
            str(DESIGN_SCRIPT),
            "--pdb_path",              str(pdb_path),
            "--jsonl_path",            str(parsed_json),
            "--chain_id_jsonl",        str(assigned_json),
            "--fixed_positions_jsonl", str(path_fixed),
            "--out_folder",            str(output_dir),
            "--num_seq_per_target",    "8",
            "--sampling_temp",         "0.5",
            "--seed",                  "37",
            "--batch_size",            "1",
            "--path_to_model_weights", str(Path("../pHMPNN_model_weights")),
            "--model_name",            model_weight,
        ]
        run_command(cmd_design)

        return (uniprot_id, model_weight, True, "Success")

    except subprocess.CalledProcessError as e:
        return (uniprot_id, model_weight, False, f"CalledProcessError: {e}")
    except Exception as e:
        return (uniprot_id, model_weight, False, f"Exception: {e}")


# ──────────────────────────────────────────────────────────────────────────────
# Main Pipeline
# ──────────────────────────────────────────────────────────────────────────────

def main():
    # 1) Verify helper scripts exist
    for script in (PARSE_SCRIPT, ASSIGN_SCRIPT, MAKE_FIXED_SCRIPT, DESIGN_SCRIPT):
        if not script.exists():
            logger.error(f"Required script not found: {script}")
            sys.exit(1)

    # 2) Load fixed‐positions dictionary
    fixed_positions_dict = load_fixed_positions(FIXED_POSITIONS_CSV)

    # 3) For each protease_folder / pdb_folder: parse + assign once
    all_jobs = []  # collect jobs for parallel processing

    for protease_folder in PROTEASE_FOLDERS:
        if not protease_folder.exists():
            logger.warning(f"Protease folder missing: {protease_folder}. Skipping.")
            continue

        protease_name = protease_folder.name  # e.g. "Cysteine_Proteases"

        for pdb_folder in PDB_FOLDERS:
            # Skip special cases
            if (
                protease_name in SPECIAL_SKIP
                and pdb_folder in SPECIAL_SKIP[protease_name]
            ):
                logger.info(f"Skipping {protease_name}/{pdb_folder} (no PDBs here).")
                continue

            folder_with_pdbs = protease_folder / pdb_folder
            if not folder_with_pdbs.exists():
                logger.warning(f"PDB folder missing: {folder_with_pdbs}. Skipping.")
                continue

            pdb_list = sorted(folder_with_pdbs.glob("*.pdb"))
            if not pdb_list:
                logger.info(f"No .pdb files found in {folder_with_pdbs}. Skipping.")
                continue

            # Prepare output base for this folder
            output_base = OUTPUT_BASE_DIR / protease_name / pdb_folder
            output_base.mkdir(parents=True, exist_ok=True)

            # Paths for parsed and assigned JSONL
            parsed_json   = output_base / "parsed_pdbs.jsonl"
            assigned_json = output_base / "assigned_pdbs.jsonl"

            # ─── Step 1: parse_multiple_chains.py (once) ───────────────────
            if needs_reparse(parsed_json, folder_with_pdbs):
                cmd_parse = [
                    sys.executable,
                    str(PARSE_SCRIPT),
                    "--input_path",  str(folder_with_pdbs),
                    "--output_path", str(parsed_json),
                ]
                try:
                    logger.info(f"Parsing all .pdb in {folder_with_pdbs} → {parsed_json}")
                    run_command(cmd_parse)
                except subprocess.CalledProcessError:
                    logger.exception(f"Failed to parse folder: {folder_with_pdbs}")
                    continue  # skip assigning & design for this folder
            else:
                logger.info(f"Skipping parse (up‐to‐date): {parsed_json}")

            # ─── Step 2: assign_fixed_chains.py (once) ────────────────────
            if need_reassign(assigned_json, parsed_json):
                cmd_assign = [
                    sys.executable,
                    str(ASSIGN_SCRIPT),
                    "--input_path",   str(parsed_json),
                    "--output_path",  str(assigned_json),
                    "--chain_list",   "A",
                ]
                try:
                    logger.info(f"Assigning chains in {parsed_json} → {assigned_json}")
                    run_command(cmd_assign)
                except subprocess.CalledProcessError:
                    logger.exception(f"Failed to assign chains for: {parsed_json}")
                    continue  # skip design for this folder
            else:
                logger.info(f"Skipping assign (up‐to‐date): {assigned_json}")

            # ─── Build jobs for each (model_weight, pdb_file) ─────────────
            for model_weight in MODEL_WEIGHTS:
                # Skip invalid model_weight for this pdb_folder
                if model_weight in SKIP_COMBINATIONS.get(pdb_folder, []):
                    logger.info(f"Skipping invalid combo: {pdb_folder} + {model_weight}")
                    continue

                bin_folder = model_weight.split("_")[-1]  # e.g. "bin1"
                for pdb_path in pdb_list:
                    uniprot_id = pdb_path.stem  # filename without ".pdb"
                    fixed_list = fixed_positions_dict.get(uniprot_id)
                    if fixed_list is None:
                        logger.warning(f"No fixed positions entry for {uniprot_id}. Skipping {pdb_path.name}.")
                        continue

                    job = {
                        "protease_name":    protease_name,
                        "pdb_folder":       pdb_folder,
                        "model_weight":     model_weight,
                        "uniprot_id":       uniprot_id,
                        "fixed_list":       fixed_list,
                        "folder_with_pdbs": folder_with_pdbs,
                        "parsed_json":      parsed_json,
                        "assigned_json":    assigned_json,
                    }
                    all_jobs.append(job)

    # If no jobs were found, exit early
    if not all_jobs:
        logger.info("No design jobs to run. Exiting.")
        return

    # ──────────────────────────────────────────────────────────────────────────
    # Step 3: Parallel “make_fixed_positions_dict” + “protein_mpnn_run.py”
    # ──────────────────────────────────────────────────────────────────────────
    logger.info(f"Submitting {len(all_jobs)} design jobs to a pool of {MAX_WORKERS or 'auto'} workers.")

    # If MAX_WORKERS is None, ProcessPoolExecutor defaults to os.cpu_count()
    with ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
        future_to_job = {executor.submit(process_single_pdb, job): job for job in all_jobs}

        for future in as_completed(future_to_job):
            job_info = future_to_job[future]
            uniprot_id   = job_info["uniprot_id"]
            model_weight = job_info["model_weight"]
            try:
                uni_id, mw, success, message = future.result()
                if success:
                    logger.info(f"[{uni_id} | {mw}] Completed successfully.")
                else:
                    logger.warning(f"[{uni_id} | {mw}] Failed: {message}")
            except Exception as exc:
                logger.exception(f"[{uniprot_id} | {model_weight}] Raised an exception: {exc}")

    logger.info("All jobs have finished.")


if __name__ == "__main__":
    main()


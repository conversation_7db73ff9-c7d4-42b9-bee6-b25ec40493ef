#!/usr/bin/env python3

import os
import subprocess
import sys
import argparse


def main():
    parser = argparse.ArgumentParser(
        description="Run the pH-dependent design pipeline on a single PDB file"
    )
    parser.add_argument(
        "--protease_folder",
        required=True,
        help="Path to the protease super-folder (e.g. ../inputs/dataset/Cysteine_Proteases/)",
    )
    parser.add_argument(
        "--pdb_folder", required=True, help="Name of the pH subfolder (e.g. ph_8_12)"
    )
    parser.add_argument(
        "--model_weight",
        required=True,
        help="Which pHMPNN weight to use (e.g. p_48_bin1)",
    )
    parser.add_argument(
        "--fixed_positions",
        required=True,
        help="Comma-separated list of 1-based positions to keep fixed",
    )
    parser.add_argument(
        "--pdb_file",
        required=True,
        help="The PDB filename in that subfolder (e.g. P95493.pdb)",
    )
    args = parser.parse_args()

    # --- Determine full path to the PDB ---
    folder_with_pdbs = os.path.join(args.protease_folder, args.pdb_folder)
    pdb_target = os.path.join(folder_with_pdbs, args.pdb_file)

    if not os.path.isfile(pdb_target):
        sys.exit(f"Error: Could not find PDB file {pdb_target}")

    # --- Extract just the protease-folder name (e.g. "Cysteine_Proteases") ---
    protease_name = os.path.basename(os.path.normpath(args.protease_folder))

    # --- Build the new output hierarchy: ../outputs/<protease_name>/<model_weight>/<uniport_id> ---
    uniport_id = args.pdb_file.split(".")[0]
    output_dir = os.path.join(
        "..", "outputs", protease_name, args.model_weight, uniport_id
    )
    os.makedirs(output_dir, exist_ok=True)

    # --- Paths for intermediate JSONL files ---
    path_for_parsed_chains = os.path.join(output_dir, "parsed_pdbs.jsonl")
    path_for_assigned_chains = os.path.join(output_dir, "assigned_pdbs.jsonl")
    path_for_fixed_positions = os.path.join(output_dir, "fixed_pdbs.jsonl")

    chains_to_design = "A"
    fixed_positions = args.fixed_positions
    model_weights_dir = "../pHMPNN_model_weights"

    # --- Locate the helper scripts (assumed to be in ../helper_scripts/) ---
    parse_script = os.path.join("..", "helper_scripts", "parse_multiple_chains.py")
    assign_script = os.path.join("..", "helper_scripts", "assign_fixed_chains.py")
    make_fixed_script = os.path.join(
        "..", "helper_scripts", "make_fixed_positions_dict.py"
    )
    design_script = os.path.join("..", "protein_mpnn_run.py")

    # --- Step 1: Parse all chains in folder_with_pdbs (produces parsed_pdbs.jsonl) ---
    cmd_parse = [
        sys.executable,
        parse_script,
        "--input_path",
        folder_with_pdbs,
        "--output_path",
        path_for_parsed_chains,
    ]
    print(f"Running: {' '.join(cmd_parse)}")
    subprocess.run(cmd_parse, check=True)

    # --- Step 2: Assign designable chains (chain "A") and mark others as fixed ---
    cmd_assign = [
        sys.executable,
        assign_script,
        "--input_path",
        path_for_parsed_chains,
        "--output_path",
        path_for_assigned_chains,
        "--chain_list",
        chains_to_design,
    ]
    print(f"Running: {' '.join(cmd_assign)}")
    subprocess.run(cmd_assign, check=True)

    # --- Step 3: Create fixed-positions dictionary for chain A ---
    cmd_fixed = [
        sys.executable,
        make_fixed_script,
        "--input_path",
        path_for_parsed_chains,
        "--output_path",
        path_for_fixed_positions,
        "--chain_list",
        chains_to_design,
        "--position_list",
        fixed_positions,
    ]
    print(f"Running: {' '.join(cmd_fixed)}")
    subprocess.run(cmd_fixed, check=True)

    # --- Step 4: Run pHMPNN design on this single PDB ---
    cmd_design = [
        sys.executable,
        design_script,
        "--pdb_path",
        pdb_target,
        "--jsonl_path",
        path_for_parsed_chains,
        "--chain_id_jsonl",
        path_for_assigned_chains,
        "--fixed_positions_jsonl",
        path_for_fixed_positions,
        "--out_folder",
        output_dir,
        "--num_seq_per_target",
        "8",
        "--sampling_temp",
        "0.5",
        "--seed",
        "37",
        "--batch_size",
        "1",
        "--path_to_model_weights",
        model_weights_dir,
        "--model_name",
        args.model_weight,
    ]
    print(f"Running: {' '.join(cmd_design)}")
    subprocess.run(cmd_design, check=True)

    print("Pipeline completed successfully!")


if __name__ == "__main__":
    main()
